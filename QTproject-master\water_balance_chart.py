#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
注水/吹除平衡图表组件
用于显示潜艇导弹发射过程中的平衡状态变化
"""

import sys
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel
from PyQt5.QtCore import QTimer, pyqtSignal, Qt
from PyQt5.QtGui import QFont
import math

class WaterBalanceChart(QWidget):
    """注水/吹除平衡图表组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumSize(300, 250)
        self.setMaximumSize(400, 350)

        # 设置深色主题样式，与邻桶效应图表保持一致
        self.setStyleSheet("""
            QWidget {
                background-color: rgba(0, 20, 40, 0.8);
                border: 1px solid #00FFFF;
                border-radius: 5px;
            }
        """)
        
        # 初始化数据
        self.missile_count = 0
        self.firing_sequence = []
        self.balance_data = []
        self.time_points = []
        
        # 平衡计算参数
        self.initial_balance = 1.0  # 初始完美平衡
        self.missile_weight = 1.5   # 每发导弹重量（吨）
        self.submarine_displacement = 150.0  # 潜艇排水量（吨）
        self.injection_rate = 0.8   # 注水补偿率
        self.system_efficiency = 0.95  # 系统效率
        self.response_delay = 0.3   # 系统响应延迟
        
        # 动态展示相关
        self.current_step = 0
        self.animation_timer = QTimer()
        self.animation_timer.timeout.connect(self.update_animation_step)
        
        self.init_ui()
        
    def init_ui(self):
        """初始化UI布局"""
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)

        # 标题，与邻桶效应图表保持一致的样式
        self.title_label = QLabel("注水/吹除平衡图")
        self.title_label.setAlignment(Qt.AlignCenter)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #00FFFF;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
                border: none;
                margin: 1px;
            }
        """)
        layout.addWidget(self.title_label)
        
        # 图表区域
        self.setup_chart()
        layout.addWidget(self.canvas)

        self.setLayout(layout)

    def cleanup_chart(self):
        """清理图表资源"""
        try:
            if hasattr(self, 'animation_timer') and self.animation_timer:
                self.animation_timer.stop()
            if hasattr(self, 'canvas') and self.canvas:
                self.canvas.close()
            if hasattr(self, 'figure') and self.figure:
                self.figure.clear()
                self.figure = None
        except Exception as e:
            print(f"清理图表资源时出错: {e}")

    def setup_chart(self):
        """设置matplotlib图表"""
        # 清理可能存在的旧图表
        if hasattr(self, 'figure'):
            self.figure.clear()
        if hasattr(self, 'canvas'):
            self.canvas.close()

        # 创建matplotlib图表，调整大小以适应增大的空间，与邻桶效应图表保持一致
        self.figure = Figure(figsize=(4.0, 3.2), dpi=80)
        self.figure.patch.set_facecolor('none')  # 透明背景

        # 创建子图
        self.ax = self.figure.add_subplot(111)
        self.ax.set_facecolor('none')  # 透明背景

        # 设置网格
        self.ax.grid(True, alpha=0.3, color='gray', linestyle='--', linewidth=0.5)

        # 设置标签和标题，调整字体大小适应较小空间
        self.ax.set_xlabel('已发射导弹数量', color='white', fontsize=9)
        self.ax.set_ylabel('平衡系数', color='white', fontsize=9)

        # 设置坐标轴颜色，调整字体大小
        self.ax.tick_params(colors='white', labelsize=7)
        self.ax.spines['bottom'].set_color('#00FFFF')
        self.ax.spines['top'].set_color('#00FFFF')
        self.ax.spines['left'].set_color('#00FFFF')
        self.ax.spines['right'].set_color('#00FFFF')

        # 设置Y轴范围和安全区域
        self.ax.set_ylim(0.0, 1.1)

        # 绘制安全区域
        self.ax.axhspan(0.8, 1.1, alpha=0.2, color='green', label='安全区域')
        self.ax.axhspan(0.6, 0.8, alpha=0.2, color='yellow', label='警告区域')
        self.ax.axhspan(0.0, 0.6, alpha=0.2, color='red', label='危险区域')

        # 绘制安全阈值线
        self.ax.axhline(y=0.8, color='orange', linestyle='--', linewidth=2, alpha=0.8, label='安全下限')
        self.ax.axhline(y=0.6, color='red', linestyle='--', linewidth=2, alpha=0.8, label='危险阈值')

        # 创建画布
        self.canvas = FigureCanvas(self.figure)
        self.canvas.setStyleSheet("background-color: transparent; border: none;")
        self.canvas.setParent(self)

        # 调整布局，减少边距
        self.figure.tight_layout(pad=0.5)
        
    def calculate_balance_value(self, fired_count, time_offset=0):
        """
        计算平衡系数值
        
        Args:
            fired_count: 已发射导弹数量
            time_offset: 时间偏移（用于模拟系统响应）
        
        Returns:
            float: 平衡系数 (0-1.0)
        """
        if fired_count == 0:
            return self.initial_balance
        
        # 基础失衡计算：导弹重量对潜艇平衡的影响
        weight_impact = (fired_count * self.missile_weight) / self.submarine_displacement
        base_imbalance = weight_impact * 0.3  # 重量影响系数
        
        # 注水补偿计算：随时间增加的补偿效果
        compensation_factor = 1.0 - math.exp(-time_offset * 2.0)  # 指数恢复
        water_compensation = base_imbalance * self.injection_rate * compensation_factor
        
        # 系统损耗：随发射次数累积的效率下降
        system_loss = fired_count * 0.01 * (1 - self.system_efficiency)
        
        # 最终平衡值计算
        balance = self.initial_balance - base_imbalance + water_compensation - system_loss
        
        # 添加轻微的随机波动，模拟真实系统
        noise = np.random.normal(0, 0.005)  # 小幅随机波动
        balance += noise
        
        # 确保值在合理范围内
        return max(0.0, min(1.0, balance))
    
    def update_chart(self, firing_sequence, total_missiles):
        """
        更新图表数据
        
        Args:
            firing_sequence: 发射序列列表
            total_missiles: 导弹总数
        """
        self.firing_sequence = firing_sequence.copy()
        self.missile_count = total_missiles
        
        # 重置动态展示
        self.current_step = 0
        self.balance_data = []
        self.time_points = []
        
        # 计算完整的平衡数据
        self.calculate_full_balance_data()
        
        # 更新标题
        self.title_label.setText(f"注水/吹除平衡图 (共{total_missiles}发导弹)")

        # 开始动态展示
        self.start_dynamic_display()
    
    def calculate_full_balance_data(self):
        """计算完整的平衡数据序列"""
        self.balance_data = []
        self.time_points = []
        
        # 初始状态
        self.balance_data.append(self.initial_balance)
        self.time_points.append(0)
        
        # 为每次发射计算平衡变化
        for i, missile_id in enumerate(self.firing_sequence):
            fired_count = i + 1
            
            # 发射瞬间的失衡（立即效应）
            instant_balance = self.calculate_balance_value(fired_count, 0)
            self.balance_data.append(instant_balance)
            self.time_points.append(fired_count)
            
            # 系统恢复过程（延迟效应）
            if fired_count < len(self.firing_sequence):  # 不是最后一发
                recovery_balance = self.calculate_balance_value(fired_count, self.response_delay)
                self.balance_data.append(recovery_balance)
                self.time_points.append(fired_count + 0.5)
    
    def start_dynamic_display(self):
        """开始动态展示"""
        if not self.firing_sequence:
            return
        
        self.current_step = 0
        self.animation_timer.start(800)  # 800ms间隔
    
    def update_animation_step(self):
        """更新动画步骤"""
        if self.current_step >= len(self.balance_data):
            self.animation_timer.stop()
            return
        
        # 清除之前的绘图
        self.ax.clear()
        
        # 重新设置图表基础元素
        self.setup_chart_base()
        
        # 绘制当前步骤的数据
        current_x = self.time_points[:self.current_step + 1]
        current_y = self.balance_data[:self.current_step + 1]
        
        if len(current_x) > 0:
            # 绘制主曲线，与邻桶效应图表保持一致的样式
            self.ax.plot(current_x, current_y, 'o-', color='#00FFFF', linewidth=2,
                        markersize=4, markerfacecolor='#00FFFF', markeredgecolor='white',
                        markeredgewidth=0.5, label='平衡状态')

            # 填充曲线下方区域
            self.ax.fill_between(current_x, current_y, alpha=0.3, color='#00FFFF')
            
            # 标注当前值
            if len(current_y) > 0:
                current_value = current_y[-1]
                current_pos = current_x[-1]
                
                # 根据平衡值确定颜色
                if current_value >= 0.8:
                    color = 'green'
                    status = '安全'
                elif current_value >= 0.6:
                    color = 'orange'
                    status = '警告'
                else:
                    color = 'red'
                    status = '危险'
                
                self.ax.annotate(f'{status}\n{current_value:.3f}',
                               xy=(current_pos, current_value),
                               xytext=(5, 5), textcoords='offset points',
                               fontsize=7, color=color, weight='bold',
                               bbox=dict(boxstyle='round,pad=0.2', facecolor='black', alpha=0.7))
        
        # 设置X轴范围
        max_x = max(len(self.firing_sequence), 1)
        self.ax.set_xlim(-0.5, max_x + 0.5)
        
        # 添加图例，调整字体大小
        self.ax.legend(loc='upper right', fontsize=7, framealpha=0.8)
        
        # 更新画布
        self.canvas.draw()
        
        # 下一步
        self.current_step += 1
    
    def setup_chart_base(self):
        """设置图表基础元素"""
        self.ax.set_facecolor('none')  # 透明背景
        self.ax.grid(True, alpha=0.3, color='gray', linestyle='--', linewidth=0.5)

        # 设置标签，调整字体大小适应较小空间
        self.ax.set_xlabel('已发射导弹数量', color='white', fontsize=9)
        self.ax.set_ylabel('平衡系数', color='white', fontsize=9)

        # 设置坐标轴颜色，调整字体大小
        self.ax.tick_params(colors='white', labelsize=7)
        self.ax.spines['bottom'].set_color('#00FFFF')
        self.ax.spines['top'].set_color('#00FFFF')
        self.ax.spines['left'].set_color('#00FFFF')
        self.ax.spines['right'].set_color('#00FFFF')
        
        # 设置Y轴范围
        self.ax.set_ylim(0.0, 1.1)
        
        # 绘制安全区域
        self.ax.axhspan(0.8, 1.1, alpha=0.2, color='green', label='安全区域')
        self.ax.axhspan(0.6, 0.8, alpha=0.2, color='yellow', label='警告区域')
        self.ax.axhspan(0.0, 0.6, alpha=0.2, color='red', label='危险区域')
        
        # 绘制阈值线
        self.ax.axhline(y=0.8, color='orange', linestyle='--', linewidth=2, alpha=0.8)
        self.ax.axhline(y=0.6, color='red', linestyle='--', linewidth=2, alpha=0.8)
    
    def stop_animation(self):
        """停止动画"""
        if self.animation_timer.isActive():
            self.animation_timer.stop()
    
    def reset_chart(self):
        """重置图表"""
        self.stop_animation()
        self.firing_sequence = []
        self.balance_data = []
        self.time_points = []
        self.current_step = 0
        
        # 清除图表并重新设置
        self.ax.clear()
        self.setup_chart_base()
        self.canvas.draw()

if __name__ == "__main__":
    # 测试代码
    from PyQt5.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    chart = WaterBalanceChart()
    chart.show()
    
    # 测试数据
    test_sequence = [1, 3, 5, 7, 2, 4, 6, 8]
    chart.update_chart(test_sequence, 8)
    
    sys.exit(app.exec_())
